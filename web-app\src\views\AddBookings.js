import {
  FormControl,
  FormControlLabel,
  FormLabel,
  Grid,
  MenuItem,
  Modal,
  Radio,
  RadioGroup,
  Select,
  TextField,
  Typography,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Box,
} from '@mui/material';
import CircularProgress from '@mui/material/CircularProgress';
import DialogActions from '@mui/material/DialogActions';
import { makeStyles } from '@mui/styles';
import { api } from 'common';
import { BookingModalBody, validateBookingObj } from 'common/sharedFunctions';
import Button from "components/CustomButtons/Button.js";
import { useEffect, useRef, useState } from 'react';
import { Toast, ToastContainer } from 'react-bootstrap';
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate } from 'react-router-dom';
import { calcEst, FONT_FAMILY, MAIN_COLOR, optionsRequired, SECONDORY_COLOR, showEst } from '../common/sharedFunctions';
import AlertDialog from '../components/AlertDialog';
import GoogleMapsAutoComplete from '../components/GoogleMapsAutoComplete';
import { colors } from '../components/Theme/WebTheme';
import UsersCombo from '../components/UsersCombo';
import SaveIcon from '@mui/icons-material/Save';
import BookmarkIcon from '@mui/icons-material/Bookmark';
import HomeIcon from '@mui/icons-material/Home';
import WorkIcon from '@mui/icons-material/Work';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import DeleteIcon from '@mui/icons-material/Delete';

const useStyles = makeStyles(theme => ({
  root: {
    '& > *': {
      margin: theme.spacing(1),
    },
  },
  '@global': {
    body: {
      backgroundColor: theme.palette.common.white,
    },
    fontFamily:FONT_FAMILY
  },
  typography:{
    fontFamily:FONT_FAMILY,
  },
  modal: {
    display: 'flex',
    padding: theme.spacing(1),
    alignItems: 'center',
    justifyContent: 'center',
  },
  paper: {
    width:480,
    backgroundColor: theme.palette.background.paper,
    border: `2px solid ${colors.BLACK}`,
    boxShadow: theme.shadows[5],
    padding: theme.spacing(2, 4, 3),
  },
  container: {
    marginTop: theme.spacing(1),
    backgroundColor: MAIN_COLOR,
    alignContent: 'center',
    borderRadius: "8px",
    width: '70%',
    [theme.breakpoints.down('md')]: { 
      width: '100%',
    },
   
  },
  container1: {
    backgroundColor: colors.LandingPage_Background,
    borderTopLeftRadius:"0px",
    borderTopRightRadius:"0px",
    borderBottomLeftRadius: "8px",
    borderBottomRightRadius: "8px",
    padding:'30px',
    width: '100%',
    top: "19px",
    boxShadow: `0px 2px 5px ${SECONDORY_COLOR}`,
  },
  title: {
    color: colors.WHITE,
    padding: '10px',
    backgroundColor:MAIN_COLOR,
    fontFamily:FONT_FAMILY,
    borderRadius:"10px",
    fontSize:18,

  },
  gridcontainer: {
    alignContent: 'center'
  },
  items: {
    margin: 0,
    width: '100%'
  },
  input: {
    fontSize: 18,
    color: colors.BLACK,
    fontFamily:FONT_FAMILY,
  },
  inputdimmed: {
    fontSize: 18,
    color: colors.CARD_LABEL,
    fontFamily:FONT_FAMILY,
  },
  carphoto: {
    height: '18px',
    marginRight: '10px'
  },
  carphotoRtl:{
    height: '16px',
    marginLeft:'10px'
  },
  buttonStyle: {
    margin: 0,
    width: '100%',
    height: 40,
    borderRadius: "30px",
    backgroundColor: MAIN_COLOR,
    color:colors.WHITE,
    fontFamily:FONT_FAMILY,
  },
  buttonStyle1:{
    backgroundColor: MAIN_COLOR,
    fontFamily:FONT_FAMILY
  },
  buttonStyle2:{
    backgroundColor: SECONDORY_COLOR,
    fontFamily:FONT_FAMILY
  },
  inputRtl: {
    "& label": {
      right: 25,
      left: "auto",
      fontFamily:FONT_FAMILY,
    },
    "& legend": {
      textAlign: "right",
      marginRight: 18,
      fontFamily:FONT_FAMILY,
    },
    "& label.Mui-focused": {
      color: MAIN_COLOR,
    },
    "& .MuiInput-underline:after": {
      borderBottomColor: MAIN_COLOR,
    },
    "& .MuiFilledInput-underline:after": {
      borderBottomColor: MAIN_COLOR,
    },
    "& .MuiOutlinedInput-root": {
      "&.Mui-focused fieldset": {
        borderColor: MAIN_COLOR,
      },
    },
    "& input": {
      fontFamily: FONT_FAMILY,
    },
  },
  rightRty:{
    "& legend": {
      marginRight: 30,
      fontFamily:FONT_FAMILY,
    },
    "& input": {
      fontFamily: FONT_FAMILY,
    },
  },
  textField: {
    "& label.Mui-focused": {
      color: MAIN_COLOR,
    },
    "& .MuiInput-underline:after": {
      borderBottomColor: MAIN_COLOR,
    },
    "& .MuiFilledInput-underline:after": {
      borderBottomColor: MAIN_COLOR,
    },
    "& .MuiOutlinedInput-root": {
      "&.Mui-focused fieldset": {
        borderColor: MAIN_COLOR,
      },
    },
    "& input": {
      fontFamily: FONT_FAMILY,
    },
  },
}));

const icons = {
  'paypal':require('../assets/payment-icons/paypal-logo.png').default,
  'braintree':require('../assets/payment-icons/braintree-logo.png').default,
  'stripe':require('../assets/payment-icons/stripe-logo.png').default,
  'paytm':require('../assets/payment-icons/paytm-logo.png').default,
  'payulatam':require('../assets/payment-icons/payulatam-logo.png').default,
  'flutterwave':require('../assets/payment-icons/flutterwave-logo.png').default,
  'paystack':require('../assets/payment-icons/paystack-logo.png').default,
  'securepay':require('../assets/payment-icons/securepay-logo.png').default,
  'payfast':require('../assets/payment-icons/payfast-logo.png').default,
  'liqpay':require('../assets/payment-icons/liqpay-logo.png').default,
  'culqi':require('../assets/payment-icons/culqi-logo.png').default,
  'mercadopago':require('../assets/payment-icons/mercadopago-logo.png').default,
  'squareup':require('../assets/payment-icons/squareup-logo.png').default,
  'wipay':require('../assets/payment-icons/wipay-logo.png').default,
  'test':require('../assets/payment-icons/test-logo.png').default,
  'razorpay':require('../assets/payment-icons/razorpay-logo.png').default,
  'paymongo':require('../assets/payment-icons/paymongo-logo.png').default,
  'iyzico':require('../assets/payment-icons/iyzico-logo.png').default,
  'slickpay':require('../assets/payment-icons/slickpay-logo.png').default
}

export default function AddBookings(props) {
  const { t, i18n  } = useTranslation();
  const isRTL = i18n.dir();
  const {
    getEstimate,
    clearEstimate,
    addBooking,
    clearBooking,
    MinutesPassed,
    GetDateString,
    GetDistance,
    updateBooking,
    updateProfileWithEmail,
    checkUserExists,
    fetchAddresses,
    editAddress,
    storeAddresses
  } = api;
  const dispatch = useDispatch();
  const classes = useStyles();
  const cartypes = useSelector(state => state.cartypes.cars);
  const state1 = useSelector(state => state);
  // console.log(state1, "state1");
  const estimatedata = useSelector(state => state.estimatedata);
  const bookingdata = useSelector(state => state.bookingdata);
  const userdata = useSelector(state => state.usersdata);
  const auth = useSelector(state => state.auth);
  const settings = useSelector(state => state.settingsdata.settings);
  const providers = useSelector(state => state.paymentmethods.providers);
  const activeBookings = useSelector(state => state.bookinglistdata.active);
  const farelistdata = useSelector(state => state.farelist.fare );
  const [carType, setCarType] = useState(t('select_car'));
  const [pickupAddress, setPickupAddress] = useState(null);
  const [dropAddress, setDropAddress] = useState(null);
  const [optionModalStatus, setOptionModalStatus] = useState(false);
  const [estimateModalStatus, setEstimateModalStatus] = useState(false);
  const [selectedCarDetails, setSelectedCarDetails] = useState(null);
  const [users, setUsers] = useState(null);
  const [commonAlert, setCommonAlert] = useState({ open: false, msg: '' });
  const [userCombo, setUserCombo] = useState(null);
  const [estimateRequested, setEstimateRequested] = useState(false);
  const [bookingType, setBookingType] = useState('Book Later');
  const rootRef = useRef(null);
  const [tempRoute, setTempRoute] = useState();
  const [drivers,setDrivers] = useState([]);
  const [paymentModalStatus, setPaymentModalStatus] = useState(false);
  const [walletModalStatus, setWalletModalStatus] = useState(false);
  const {state} = useLocation();
  const [selectedProvider, setSelectedProvider] = useState();
  const [selectedProviderIndex, setSelectedProviderIndex] = useState(0);
  const [roundTrip, setRoundTrip] = useState(0);
  const [tripInstructions, setTripInstructions] = useState('');
  const [loading, setLoading] = useState(false);
  const [bookingOnWait,setBookingOnWait] = useState();
  const [deliveryWithBid, setDeliveryWithBid] = useState(0);
  const [otherPerson, setOtherPerson] = useState(false);
  let baseFare ;
  const [isBooked, setIsBooked] = useState(false);
  const [tripType, setTripType] = useState('ONE WAY TRIP');
  const [flightNumber, setFlightNumber] = useState('');
  const [noteText, setNoteText] = useState('');
  const [tips, setTips] = useState('');
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [returnDate, setReturnDate] = useState(GetDateString());
  const [returnFlightNumber, setReturnFlightNumber] = useState('');

  // Saved addresses functionality
  const [savedAddresses, setSavedAddresses] = useState([]);
  const [saveAddressModalOpen, setSaveAddressModalOpen] = useState(false);
  const [savedAddressesModalOpen, setSavedAddressesModalOpen] = useState(false);
  const [addressToSave, setAddressToSave] = useState(null);
  const [addressName, setAddressName] = useState('');
  const [saveAddressType, setSaveAddressType] = useState('');
  const [savingAddress, setSavingAddress] = useState(false);
  const [currentAddressField, setCurrentAddressField] = useState(''); // 'pickup' or 'drop'


  useEffect(() => {
    if (settings && settings.bookingFlow) {
        setDeliveryWithBid(settings.bookingFlow === "2" ? 1 : 0)
    }
  }, [settings, settings.bookingFlow])



  const [instructionData,setInstructionData] = useState({
    otherPerson : "",
    otherPersonPhone: "",
    pickUpInstructions: "",
    deliveryInstructions: "",
    parcelTypeIndex: 0,
    optionIndex: 0,
    parcelTypeSelected: null,
    optionSelected: null
  });

  const [profileData,setProfileData] = useState({
    firstName : '',
    lastName : '',
    email: ''
  });

  const [payment_mode, setPaymentMode] = useState(0);
  const [radioProps,setRadioProps] =  useState([]);

  const navigate = useNavigate();

  // useEffect(()=>{
  //     if(settings && providers){
  //       setSelectedProvider(providers[0]);
  //       let arr = [{ label: t('wallet'), value: 0, cat: 'wallet' }]; 
  //       let val = 0;
  //       if(!settings.disable_online && providers && providers.length > 0){
  //           val++;
  //           arr.push({ label: t('card'), value: val , cat: 'card' });
  //       }
  //       if(!settings.disable_cash){
  //           val++;
  //           arr.push({ label: t('cash'), value: val, cat: 'cash' });
  //       }
  //       setRadioProps(arr);
  //     }
  // },[settings, providers, t]);
  useEffect(() => {
    if (settings && providers) {
      // Remove wallet option and set cash as default
      let arr = [];
      let val = 0;
  
      if (!settings.disable_cash) {
        // Add cash as the default option
        arr.push({ label: t('cash'), value: val, cat: 'cash' });
        setSelectedProvider('cash');
      }
  
      if (!settings.disable_online && providers && providers.length > 0) {
        val++;
        arr.push({ label: t('card'), value: val, cat: 'card' });
      }
  
      setRadioProps(arr);
    }
  }, [settings, providers, t]);
  

  useEffect(() => {
    if(state && state !== null){
      let carDetails = state.carData;
      setCarType(carDetails.name)
      setInstructionData({
        deliveryPerson : "",
        deliveryPersonPhone: "",
        pickUpInstructions: "",
        deliveryInstructions: "",
        parcelTypeIndex: 0,
        optionIndex: 0,
        parcelTypeSelected: Array.isArray(carDetails.parcelTypes) ? carDetails.parcelTypes[0] : null,
        optionSelected: Array.isArray(carDetails.options) ? carDetails.options[0] : null
      })
      setSelectedCarDetails(carDetails);
    }
  }, [state]);

  const handleChange = (e) => {
    if(e.target.name === 'parcelTypeIndex'){
      setInstructionData({ 
        ...instructionData,
        parcelTypeIndex: parseInt(e.target.value),
        parcelTypeSelected: selectedCarDetails.parcelTypes[e.target.value]
      });
    }else if(e.target.name === 'optionIndex'){
      setInstructionData({ 
        ...instructionData,
        optionIndex: parseInt(e.target.value),
        optionSelected: selectedCarDetails.options[e.target.value]
      });
    }else if(e.target.name === 'payment_mode'){
      setPaymentMode(e.target.value);
    }else if(e.target.name === 'selectedProviderIndex'){
      setSelectedProviderIndex(parseInt(e.target.value));
      setSelectedProvider(providers[parseInt(e.target.value)]);
    }else if(e.target.name === 'tripInstructions'){
      setTripInstructions(e.target.value);
    }else if(e.target.name === 'roundTrip'){
      setRoundTrip(e.target.value);
    }else if(e.target.name === 'bookingFlow'){
      setDeliveryWithBid(e.target.value);
    }else if(e.target.name === 'firstName'){
      setProfileData({ ...profileData, [e.target.name]: e.target.value });
    }else if(e.target.name === 'lastName'){
      setProfileData({ ...profileData, [e.target.name]: e.target.value });
    }else if(e.target.name === 'email'){
      setProfileData({ ...profileData, [e.target.name]: e.target.value });
    }else{
      setInstructionData({ ...instructionData, [e.target.name]: e.target.value });
    }
  };

  const [selectedDate, setSelectedDate] = useState(GetDateString());

  const clearForm = () => {
    setUserCombo(null);
    setPickupAddress(null);
    setDropAddress(null);
    setSelectedCarDetails(null);
    setCarType(t('select_car'));
    setBookingType('Book Now');
    setEstimateRequested(false);
  }

  useEffect(() => {
    if (auth.profile && bookingOnWait) {
      setEstimateModalStatus(false); 
      dispatch(addBooking(bookingOnWait));
      setBookingOnWait(null);
      setLoading(false);
    }
  }, [auth.profile,bookingOnWait,dispatch,addBooking])

  // Fetch saved addresses
  // For admin: only fetch when a user is selected; for others: fetch own addresses
  useEffect(() => {
    if (auth.profile && auth.profile.uid) {
      if (auth.profile.usertype === 'admin') {
        if (userCombo && userCombo.uid) {
          dispatch(fetchAddresses(userCombo.uid));
        }
      } else {
        dispatch(fetchAddresses(auth.profile.uid));
      }
    }
  }, [auth.profile, userCombo, dispatch, fetchAddresses]);

  // Update saved addresses from Redux state
  const addressdata = useSelector(state => state.addressdata);
  useEffect(() => {
    if (addressdata && addressdata.addresses) {
      setSavedAddresses(addressdata.addresses);
    }
  }, [addressdata]);

  // Refetch addresses when userCombo changes (for admin users)
  useEffect(() => {
    if (auth.profile.usertype === 'admin' && userCombo && userCombo.uid) {
      dispatch(fetchAddresses(userCombo.uid));
    }
  }, [userCombo, auth.profile.usertype, dispatch, fetchAddresses]);

  useEffect(() => {
    if (bookingdata.booking && bookingdata.booking.mainData.status === 'PAYMENT_PENDING') {
      if(bookingdata.booking.mainData.payment_mode === 'cash'){
        dispatch(clearBooking());
        setTempRoute(null);
        navigate('/bookings')
      } else{
        if(bookingdata.booking.mainData.payment_mode === 'card'){
          setPaymentModalStatus(true);
        }else{
          setWalletModalStatus(true);
        }
      }
    }
    if (bookingdata.booking && bookingdata.booking.mainData.status === 'NEW') {
      dispatch(clearBooking());
      setTempRoute(null);
      navigate('/bookings')
    }
  }, [bookingdata.booking,clearBooking,dispatch,navigate]);

  const handleCarSelect = (event) => {
    setCarType(event.target.value);
    let carDetails = null;
    let carspecial_fare = null;
    for (let i = 0; i < cartypes.length; i++) {
      if (cartypes[i].name === event.target.value) {
         carDetails = cartypes[i];
        let instObj = {...instructionData};
        if(Array.isArray(cartypes[i].parcelTypes)){
          instObj.parcelTypeSelected = cartypes[i].parcelTypes[0];
          instObj.parcelTypeIndex = 0;
        }
        if(Array.isArray(cartypes[i].options)){
          instObj.optionSelected = cartypes[i].options[0];
          instObj.optionIndex = 0;
        }
        setInstructionData(instObj);
      }
      if (cartypes[i]?.special_fare) {
        carspecial_fare = cartypes[i]?.special_fare;
      }
    }
    setSelectedCarDetails(carDetails);
  };

  const handleBookTypeSelect = (event) => {
    setBookingType(event.target.value);
    if (bookingType === 'Book Later') {
      setSelectedDate(GetDateString());
    }
  };

  // Update the onDateChange function (around line 526)
  const onDateChange = (event) => {
    setSelectedDate(event.target.value);
  };

  const onReturnDateChange = (event) => {
   setReturnDate(event.target.value);
  };

  useEffect(() => {
    if (estimatedata.estimate && estimateRequested) {
      setEstimateRequested(false);
      setEstimateModalStatus(true);
    }
    if (userdata.users) {
      // console.log('Debug - userdata.users:', userdata.users);
      let arr = [];
      for (let i = 0; i < userdata.users.length; i++) { 
        let user = userdata.users[i];
        if (user.usertype === 'customer' && ((auth.profile.usertype === 'fleetadmin') || auth.profile.usertype === 'admin')) {
          // console.log('Debug - user:', user);
          arr.push({
            'firstName': user.firstName,
            'lastName': user.lastName,
            'mobile': user.mobile,
            'email': user.email,
            'uid': user.id,
            'desc': user.firstName + ' ' + user.lastName + ' (' + (settings.AllowCriticalEditsAdmin? user.mobile : t("hidden_demo")) + ') ' + (settings.AllowCriticalEditsAdmin? user.email : t("hidden_demo")),
            'pushToken': user.pushToken ? user.pushToken : ''
          });
        }
      }
      setUsers(arr);
      let arrDrivers = [];
      for (let i = 0; i < userdata.users.length; i++) {
        let user = userdata.users[i];
        if ((user.usertype) && (user.usertype === 'driver') && (user.approved === true) && (user.queue === false) && (user.driverActiveStatus === true) && (user.location) && ((user.licenseImage && settings.license_image_required) || !settings.license_image_required) && ((user.carApproved && settings.carType_required) || !settings.carType_required) && ((user.term && settings.term_required) || !settings.term_required)) {
          if((auth.profile.usertype === 'fleetadmin' && user.fleetadmin === auth.profile.uid) || auth.profile.usertype === 'admin' || auth.profile.usertype === 'customer'){
            arrDrivers.push({
              'uid': user.id,
              'location': user.location,
              'carType': user.carType,
              'fleetadmin': user.fleetadmin ? user.fleetadmin : null
            });
          }
        }
      }
      setDrivers(arrDrivers);
    }
  }, [estimatedata.estimate, userdata.users, estimateRequested, settings, auth.profile.usertype, auth.profile.uid,t]);
 
 
  useEffect(() => {
    if(auth.profile.usertype && auth.profile.usertype==='customer'){
      setUserCombo({
        'firstName': auth.profile.firstName,
        'lastName': auth.profile.lastName,
        'mobile': auth.profile.mobile,
        'email': auth.profile.email,
        'uid': auth.profile.uid,
        'pushToken': auth.profile.pushToken ? auth.profile.pushToken : ''
      })
    }
  }, [auth.profile]);


  const mobile = (userCombo && userCombo.mobile ? true : false);

  const handleGetOptions = (e) => {
    e.preventDefault();
    if((settings && settings.imageIdApproval && auth.profile.usertype ==='customer' && auth.profile.verifyId) || auth.profile.usertype==='admin' || (settings && !settings.imageIdApproval && auth.profile.usertype ==='customer') || auth.profile.usertype === 'fleetadmin'){
      if(!(settings && settings.disable_cash && (auth.profile.usertype==='admin'|| auth.profile.usertype === 'fleetadmin')) || auth.profile.usertype==='customer'){
          setEstimateRequested(true);
          if (userCombo && pickupAddress && dropAddress && selectedCarDetails) {
            let pickUpPostal = '';
            let dropUpPostal= '';
            for (let i = 0; i < pickupAddress.placeDetails.address_components.length; i++) {
              for (let j = 0; j < pickupAddress.placeDetails.address_components[i].types.length; j++) {
                if (pickupAddress.placeDetails.address_components[i].types[j] === "postal_code") {
                  pickUpPostal = pickupAddress.placeDetails.address_components[i]?.long_name;
                }
              }
            }

            for (let i = 0; i < dropAddress.placeDetails.address_components.length; i++) {
              for (let j = 0; j < dropAddress.placeDetails.address_components[i].types.length; j++) {
                if (dropAddress.placeDetails.address_components[i].types[j] === "postal_code") {
                  dropUpPostal = dropAddress.placeDetails.address_components[i]?.long_name;
                }
              }
            }

            if (pickUpPostal && dropUpPostal) {
                const filteredFare = farelistdata.find(
                  fare => (fare.pincode1 === pickUpPostal && fare.pincode2 === dropUpPostal) ||
                          (fare.pincode1 === dropUpPostal && fare.pincode2 === pickUpPostal)
                );

                if (filteredFare) {
                  baseFare = filteredFare.fare;
                } else {
                  setCommonAlert({ open: true, msg: "Booking not available for this location"})
                  return false;
                }
              }else{
                setCommonAlert({ open: true, msg: "Postal code error booking not available"})
                return false;
              }
              if(!baseFare){
               setCommonAlert({ open: true, msg: "Postal code error booking not available"}) 
               return false;
              }
            const directionService = new window.google.maps.DirectionsService();
            directionService.route(
              {
                origin: new window.google.maps.LatLng(pickupAddress.coords.lat, pickupAddress.coords.lng),
                destination: new window.google.maps.LatLng(dropAddress.coords.lat, dropAddress.coords.lng),
                travelMode: window.google.maps.TravelMode.DRIVING
              },
              (result, status) => {
                if (status === window.google.maps.DirectionsStatus.OK) {
                  const route = {
                    distance_in_km:(result.routes[0].legs[0].distance.value / 1000),
                    time_in_secs:result.routes[0].legs[0].duration.value,
                    polylinePoints:result.routes[0].overview_polyline
                  };
                  setTempRoute(route);
                  if(pickupAddress && dropAddress){
                    setIsBooked(false);
                    if (bookingType === 'Book Now') {
                      setIsBooked(true);
                      if(mobile === true){
                        if(Array.isArray(selectedCarDetails.options) || Array.isArray(selectedCarDetails.parcelTypes)){
                          setOptionModalStatus(true);
                        }else{
                          let estimateRequest = {
                            pickup: pickupAddress,
                            drop: dropAddress,
                            carDetails: selectedCarDetails,
                            instructionData: instructionData,
                            routeDetails: route,
                            baseFare:baseFare
                          };
                          dispatch(getEstimate(estimateRequest))
                          .then(response => console.log(response))
                          .catch(error => console.error(error));
                        }
                      }else{
                        setCommonAlert({ open: true, msg: t('incomplete_user')});
                        setIsBooked(false);
                      }
                    } else {
                      setIsBooked(false);
                        if (bookingType === 'Book Later' && selectedDate) {
                          if (auth.profile.usertype === 'customer' ? MinutesPassed(selectedDate) >= 120 : true) {
                            setIsBooked(true);
                            if(mobile === true){
                              if(Array.isArray(selectedCarDetails.options) || Array.isArray(selectedCarDetails.parcelTypes)){
                                setOptionModalStatus(true);
                              }else{
                                let estimateRequest = {
                                  pickup: pickupAddress,
                                  drop: dropAddress,
                                  carDetails: selectedCarDetails,
                                  instructionData: instructionData,
                                  routeDetails: route,
                                  baseFare:baseFare
                                };
                                dispatch(getEstimate(estimateRequest));
                              }
                            }else{
                              setCommonAlert({ open: true, msg: t('incomplete_user')});
                              setIsBooked(false);
                            }
                          } else { 
                            // setCommonAlert({ open: true, msg: t('past_booking_error') });
                            setCommonAlert({ open: true, msg: t('past_booking_error') });
                            setIsBooked(false);
                          }
                        } else {
                          setCommonAlert({ open: true, msg: t('select_proper') });
                          setIsBooked(false);
                        }
                      }
                  } else {
                    setCommonAlert({ open: true, msg: t('place_to_coords_error') })
                    setIsBooked(false);
                  }
                }
              }
            )
          } else {
            setCommonAlert({ open: true, msg: t('select_proper') })
          }
        } else {
        setCommonAlert({ open: true, msg: t('cash_booking_false') })
      }
    } else {
      setCommonAlert({ open: true, msg: t('verifyid_error') })
    }    
  }

  const handleWalletPayment = (e) => {
    e.preventDefault();
    let curBooking = { ...bookingdata.booking.mainData };
    if(parseFloat(curBooking.trip_cost)>parseFloat(auth.profile.walletBalance) && radioProps[payment_mode].cat === 'wallet'){
      setCommonAlert({open:true, msg: t('wallet_balance_low')});
    }else{
      let requestedDrivers = {};
      if (settings.autoDispatch && bookingType === 'Book Now') {
        for (let i = 0; i < drivers.length; i++) {
          const driver = drivers[i];
          let distance = GetDistance(pickupAddress.coords.lat, pickupAddress.coords.lng, driver.location.lat, driver.location.lng);
          if (settings.convert_to_mile) {
            distance = distance / 1.609344;
          }
          if (distance < ((settings && settings.driverRadius) ? settings.driverRadius : 10) && selectedCarDetails.name === driver.carType) {
            requestedDrivers[driver['uid']] = true;
          }
        }
      }
      curBooking.prepaid = true;
      curBooking.status = 'NEW';
      curBooking.payment_mode = "wallet";
      curBooking.customer_paid = parseFloat(curBooking.trip_cost).toFixed(settings.decimal);
      curBooking.discount = 0;
      curBooking.usedWalletMoney = parseFloat(curBooking.trip_cost).toFixed(settings.decimal);
      curBooking.cardPaymentAmount = 0;
      curBooking.cashPaymentAmount = 0;
      curBooking.payableAmount = 0;
      curBooking.requestedDrivers = requestedDrivers;

      dispatch(updateBooking(curBooking));

      setTimeout(()=>{
        dispatch(clearEstimate());
        setPaymentModalStatus(false);
        setWalletModalStatus(false);
        clearForm();
        dispatch(clearBooking());
        setTempRoute(null);
        navigate('/bookings')
      },1500);
    }
  }

  const handleGetEstimate = (e) => {
    e.preventDefault();
    setOptionModalStatus(false);
    let estimateRequest = {
      pickup: pickupAddress,
      drop: dropAddress,
      carDetails: selectedCarDetails,
      instructionData: instructionData,
      routeDetails: tempRoute,
      baseFare:baseFare
    };
    dispatch(getEstimate(estimateRequest));
  };

  const confirmBooking = (e) => {
    e.preventDefault();
    let requestedDrivers = {};
    let driverEstimates = {};

    // Check if otherPerson is true, if so, skip the existing booking check
    if (!otherPerson && auth.profile.usertype !== 'fleetadmin') {
      const existingBooking = activeBookings.find(
        (booking) =>
          booking.customer === auth.profile.uid &&
          booking.tripdate === (bookingType === 'Book Later' ? new Date(selectedDate).getTime() : new Date().getTime())
      );

      if (existingBooking) {
        setCommonAlert({ open: true, msg: t('You already have a booking scheduled for this time slot.') });
        return;
      }
    }

    let notfound = true;
    if (activeBookings) {
      for (let i = 0; i < activeBookings.length; i++) {
        if (activeBookings[i].payment_mode === 'wallet' && activeBookings[i].status !== 'PAID') {
          notfound = false;
          break;
        }
      }
    }
    if (roundTrip === 1) {
      if ((radioProps[payment_mode].cat === 'wallet' && notfound && auth.profile.usertype === 'customer') || radioProps[payment_mode].cat !== 'wallet' || auth.profile.usertype === 'admin' || auth.profile.usertype === 'fleetadmin') {
        const regx1 = /([0-9\s-]{7,})(?:\s*(?:#|x\.?|ext\.?|extension)\s*(\d+))?$/;
        if ((otherPerson && /\S/.test(instructionData.otherPerson) && regx1.test(instructionData.otherPersonPhone) && instructionData.otherPersonPhone && instructionData.otherPersonPhone.length > 6) || !otherPerson) {
          if ((radioProps[payment_mode].cat === 'wallet' && (parseFloat(auth.profile.walletBalance) >= parseFloat(estimatedata.estimate.estimateFare)) && !calcEst && auth.profile.usertype === 'customer') || radioProps[payment_mode].cat !== 'wallet' || (radioProps[payment_mode].cat === 'wallet' && calcEst && auth.profile.usertype === 'customer') || auth.profile.usertype === 'admin' || auth.profile.usertype === 'fleetadmin') {
            if (settings.autoDispatch && bookingType === 'Book Now') {
              for (let i = 0; i < drivers.length; i++) {
                const driver = drivers[i];
                let distance = GetDistance(pickupAddress.coords.lat, pickupAddress.coords.lng, driver.location.lat, driver.location.lng);
                if (settings.convert_to_mile) {
                  distance = distance / 1.609344;
                }
                if (distance < ((settings && settings.driverRadius) ? settings.driverRadius : 10) && ((settings.carType_required && selectedCarDetails.name === driver.carType) || !settings.carType_required) && ((auth.profile.usertype === 'fleetadmin' && driver.fleetadmin === auth.profile.uid) || auth.profile.usertype !== 'fleetadmin')) {
                  requestedDrivers[driver['uid']] = true;
                  driverEstimates[driver['uid']] = { distance: distance, timein_text: ((distance * 2) + 1).toFixed(0) + ' min' };
                }
              }
              if (Object.keys(requestedDrivers).length === 0) {
                setCommonAlert({ open: true, msg: t('no_driver_found_alert_messege') });
                return;
              }
            }
  
            // For round trip, only dispatch addBooking ONCE with roundTrip: true and returnDate
            let bookingObject = {
              pickup: pickupAddress,
              drop: dropAddress,
              carDetails: selectedCarDetails,
              userDetails: {
                uid: userCombo.uid,
                firstName: userCombo.firstName,
                lastName: userCombo.lastName,
                mobile: userCombo.mobile,
                pushToken: userCombo.pushToken,
                email: userCombo.email || "-"
              },
              estimate: {
                ...estimatedata.estimate,
                estimateFare:(parseFloat(estimatedata.estimate.subTotal) + parseFloat(estimatedata.estimate.special_fare) + parseFloat((tips || 0) / 2)).toFixed(2), 
              },
              instructionData: instructionData,
              tripInstructions: tripInstructions,
              roundTrip: roundTrip === 0 ? "false" : "true",
              tripdate: bookingType === 'Book Later' ? new Date(selectedDate).getTime() : new Date().getTime(),
              bookLater: bookingType === 'Book Later' ? true : false,
              settings: settings,
              booking_type_admin: auth.profile.usertype === 'admin' ? true : false,
              fleetadmin: auth.profile.usertype === 'fleetadmin' ? auth.profile.uid : null,
              requestedDrivers: calcEst ? requestedDrivers : (optionsRequired && radioProps[payment_mode].cat === 'cash') ? requestedDrivers : (settings.prepaid && radioProps[payment_mode].cat === 'cash') ? requestedDrivers : !settings.prepaid ? requestedDrivers : requestedDrivers,
              driverEstimates: driverEstimates,
              payment_mode: (auth.profile.usertype === 'admin' || auth.profile.usertype === 'fleetadmin') ? 'cash' : radioProps[payment_mode].cat,
              booking_from_web: true,
              deliveryWithBid: auth.profile.usertype === 'customer' ? deliveryWithBid === 0 ? false : true : false,
              flightNumber: flightNumber,
              noteText: noteText,
              tips: tips,
              // For round trip, add returnDate and returnFlightNumber
              ...(roundTrip === 1 ? {
                returnDate: bookingType === 'Book Later' ? new Date(returnDate).getTime() : new Date().getTime(),
                returnFlightNumber: returnFlightNumber
              } : {})
            };

            // Validate and dispatch the booking
            const result = validateBookingObj(t, bookingObject, instructionData, otherPerson);
            if(result.error){
              setCommonAlert({ open: true, msg: result.msg });
            } else{
                              setEstimateModalStatus(false); 
                dispatch(addBooking(result.bookingObject)).then(() => { 
                   
                  if (roundTrip === 1) {
                    setToastMessage(t('Booking confirmed for a round trip!'));
                    console.log('Booking confirmed for a round trip!')
                  } else {
                    setToastMessage(t('Booking confirmed for a one way trip!'));
                    console.log('Booking confirmed for a one way trip!')
                  }
                  setShowToast(true);
                });
            }
          } else {
            setCommonAlert({ open: true, msg: t('wallet_balance_low') });
          }
        } else {
          setCommonAlert({ open: true, msg: t('otherPersonDetailMissing') });
        }
      } else {
        setCommonAlert({ open: true, msg: t('wallet_booking_alert') });
      }
    } else {
      if ((radioProps[payment_mode].cat === 'wallet' && notfound && auth.profile.usertype === 'customer') || radioProps[payment_mode].cat !== 'wallet' || auth.profile.usertype === 'admin' || auth.profile.usertype === 'fleetadmin') {
        const regx1 = /([0-9\s-]{7,})(?:\s*(?:#|x\.?|ext\.?|extension)\s*(\d+))?$/;
        if ((otherPerson && /\S/.test(instructionData.otherPerson) && regx1.test(instructionData.otherPersonPhone) && instructionData.otherPersonPhone && instructionData.otherPersonPhone.length > 6) || !otherPerson) {
          if ((radioProps[payment_mode].cat === 'wallet' && (parseFloat(auth.profile.walletBalance) >= parseFloat(estimatedata.estimate.estimateFare)) && !calcEst && auth.profile.usertype === 'customer') || radioProps[payment_mode].cat !== 'wallet' || (radioProps[payment_mode].cat === 'wallet' && calcEst && auth.profile.usertype === 'customer') || auth.profile.usertype === 'admin' || auth.profile.usertype === 'fleetadmin') {
            if (settings.autoDispatch && bookingType === 'Book Now') {
              for (let i = 0; i < drivers.length; i++) {
                const driver = drivers[i];
                let distance = GetDistance(pickupAddress.coords.lat, pickupAddress.coords.lng, driver.location.lat, driver.location.lng);
                if (settings.convert_to_mile) {
                  distance = distance / 1.609344;
                }
                if (distance < ((settings && settings.driverRadius) ? settings.driverRadius : 10) && ((settings.carType_required && selectedCarDetails.name === driver.carType) || !settings.carType_required) && ((auth.profile.usertype === 'fleetadmin' && driver.fleetadmin === auth.profile.uid) || auth.profile.usertype !== 'fleetadmin')) {
                  requestedDrivers[driver['uid']] = true;
                  driverEstimates[driver['uid']] = { distance: distance, timein_text: ((distance * 2) + 1).toFixed(0) + ' min' };
                }
              }
              if (Object.keys(requestedDrivers).length === 0) {
                setCommonAlert({ open: true, msg: t('no_driver_found_alert_messege') });
                return;
              }
            }
  
            let bookingObject = {
              pickup: pickupAddress,
              drop: dropAddress,
              carDetails: selectedCarDetails,
              userDetails: {
                uid: userCombo.uid,
                firstName: userCombo.firstName,
                lastName: userCombo.lastName,
                mobile: userCombo.mobile,
                pushToken: userCombo.pushToken,
                email: userCombo.email || "-"
              },
              estimate: {
                ...estimatedata.estimate,
                estimateFare:(parseFloat(estimatedata.estimate.subTotal) + parseFloat(estimatedata.estimate.special_fare) + parseFloat(tips || 0)).toFixed(2), 
              },
              instructionData: instructionData,
              tripInstructions: tripInstructions,
              roundTrip: roundTrip === 0 ? "false" : "true",
              tripdate: bookingType === 'Book Later' ? new Date(selectedDate).getTime() : new Date().getTime(),
              bookLater: bookingType === 'Book Later' ? true : false,
              settings: settings,
              booking_type_admin: auth.profile.usertype === 'admin' ? true : false,
              fleetadmin: auth.profile.usertype === 'fleetadmin' ? auth.profile.uid : null,
              requestedDrivers: calcEst ? requestedDrivers : (optionsRequired && radioProps[payment_mode].cat === 'cash') ? requestedDrivers : (settings.prepaid && radioProps[payment_mode].cat === 'cash') ? requestedDrivers : !settings.prepaid ? requestedDrivers : requestedDrivers,
              driverEstimates: driverEstimates,
              payment_mode: (auth.profile.usertype === 'admin' || auth.profile.usertype === 'fleetadmin') ? 'cash' : radioProps[payment_mode].cat,
              booking_from_web: true,
              deliveryWithBid: auth.profile.usertype === 'customer' ? deliveryWithBid === 0 ? false : true : false,
              flightNumber: flightNumber,
              noteText: noteText,
              tips: tips
              };
  
            if(auth.profile.usertype === 'customer' && !(auth.profile.firstName && auth.profile.lastName && auth.profile.email && auth.profile.firstName.length>0 && auth.profile.lastName.length>0 && auth.profile.email.length>0)){
              // const regx1 = /([0-9\s-]{7,})(?:\s*(?:#|x\.?|ext\.?|extension)\s*(\d+))?$/;
              // if ((optionsRequired && /\S/.test(instructionData.deliveryPerson) && regx1.test(instructionData.deliveryPersonPhone) && instructionData.deliveryPersonPhone && instructionData.deliveryPersonPhone.length > 6) || !optionsRequired){
                if((profileData.firstName && profileData.firstName.length > 0 && profileData.lastName && profileData.lastName.length > 0) || (auth.profile.firstName && auth.profile.firstName.length > 0 && auth.profile.lastName && auth.profile.lastName.length > 0)){
                  const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
                  if((re.test(profileData.email) && !auth.profile.email) || (auth.profile.email && auth.profile.email.length > 0)){
                    setLoading(true);
                    checkUserExists({email: profileData.email}).then(async(res) => {
                      if (res.users && res.users.length > 0) {
                        setLoading(false);
                        alert(t('user_exists'));
                      }
                      else if(res.error){
                        setLoading(false);
                        alert(t('email_or_mobile_issue'));
                      } else{
                        setLoading(false);
                        if(radioProps[payment_mode].cat === 'card'){
                          const paymentPacket = { 
                            payment_mode: 'card',
                            customer_paid: parseFloat(estimatedata.estimate.estimateFare).toFixed(settings.decimal),
                            cardPaymentAmount: parseFloat(estimatedata.estimate.estimateFare).toFixed(settings.decimal),
                            discount: 0,
                            usedWalletMoney:0,
                            cashPaymentAmount: 0,
                            promo_applied: false,
                            promo_details: null,
                            payableAmount: parseFloat(estimatedata.estimate.estimateFare).toFixed(settings.decimal),
                          };
                          bookingObject['paymentPacket'] = paymentPacket;
                        }
                
                        const result = validateBookingObj(t, bookingObject, instructionData, otherPerson);
                        if(result.error){
                          setCommonAlert({ open: true, msg: result.msg });
                        } else{
                          profileData['uid'] = auth.profile.uid;
                          let bookingData = result.bookingObject;
                          bookingData.userDetails.firstName = profileData.firstName;
                          bookingData.userDetails.lastName = profileData.lastName;
                          bookingData.userDetails.email = profileData.email;
                          setBookingOnWait(bookingData);
                        }
                        setTimeout(()=>{
                          dispatch(updateProfileWithEmail(profileData));
                        },200) 
                      }
                  });
                  } else {
                    setCommonAlert({ open: true, msg: t('proper_email') })
                  }
                } else {
                  setCommonAlert({ open: true, msg: t('proper_input_name') })
                }
              // } else {
              //   setCommonAlert({ open: true, msg: t('deliveryDetailMissing') })
              // }
            } else{
              if(radioProps[payment_mode].cat === 'card'){
                const paymentPacket = { 
                  payment_mode: 'card',
                  customer_paid: parseFloat(estimatedata.estimate.estimateFare).toFixed(settings.decimal),
                  cardPaymentAmount: parseFloat(estimatedata.estimate.estimateFare).toFixed(settings.decimal),
                  discount: 0,
                  usedWalletMoney:0,
                  cashPaymentAmount: 0,
                  promo_applied: false,
                  promo_details: null,
                  payableAmount: parseFloat(estimatedata.estimate.estimateFare).toFixed(settings.decimal),
                };
                bookingObject['paymentPacket'] = paymentPacket;
              }
              const result = validateBookingObj(t, bookingObject, instructionData, otherPerson);
              if(result.error){
                setCommonAlert({ open: true, msg: result.msg });
              } else{
                setEstimateModalStatus(false); 
                dispatch(addBooking(result.bookingObject)).then(() => {
                   

                  if (roundTrip === 1) {
                    setToastMessage(t('Booking confirmed for a round trip!'));
                    console.log('Booking confirmed for a round trip!')
                  } else {
                    setToastMessage(t('Booking confirmed for a one way trip!'));
                    console.log('Booking confirmed for a one way trip!')
                  }
                  setShowToast(true);
                });
              }
            }
          }else{
            setCommonAlert({ open: true, msg: t('wallet_balance_low') });
          } 
        } else {
          setCommonAlert({ open: true, msg: t('otherPersonDetailMissing') })
        }
      } else {
        setCommonAlert({ open: true, msg: t('wallet_booking_alert') });
      }
    }
  };

  const handleOptionModalClose = (e) => {
    e.preventDefault();
    setOptionModalStatus(false);
  };

  const handleEstimateModalClose = (e) => {
    e.preventDefault();
    setIsBooked(false);
    setEstimateModalStatus(false);
    dispatch(clearEstimate());
    setEstimateRequested(false);
  };

  const handleEstimateErrorClose = (e) => {
    e.preventDefault();
    dispatch(clearEstimate());
    setEstimateRequested(false);
  };

  const handleBookingErrorClose = (e) => {
    e.preventDefault();
    dispatch(clearBooking());
    setEstimateRequested(false);
  };

  const handleCommonAlertClose = (e) => {
    e.preventDefault();
    setCommonAlert({ open: false, msg: '' })
  };

  const handleWalletModalClose = (e) => {
    setTimeout(()=>{
      setWalletModalStatus(false);
      dispatch(clearBooking());
      dispatch(clearEstimate());
    },1500);
  }

  const handlePaymentModalClose = (e) => {
    setTimeout(()=>{
      setPaymentModalStatus(false);
      dispatch(clearBooking());
      dispatch(clearEstimate());
    },1500);
  }

  const handleTripTypeSelect = (event) => {
    setTripType(event.target.value);
    // Set roundTrip value based on trip type selection
    setRoundTrip(event.target.value === 'ROUND TRIP' ? 1 : 0);
  };

  // Saved addresses functions
  const handleSaveAddress = (address, fieldType) => {
    setAddressToSave(address);
    setCurrentAddressField(fieldType);
    setSaveAddressModalOpen(true);
  };

  // Add this helper function to get postal code from place_id
  const getPostalCodeFromPlaceId = async (placeId) => {
    if (!placeId) return null;
    
    try {
      const service = new window.google.maps.places.PlacesService(document.createElement('div'));
      return new Promise((resolve, reject) => {
        service.getDetails(
          {
            placeId: placeId,
            fields: ['address_components']
          },
          (place, status) => {
            if (status === window.google.maps.places.PlacesServiceStatus.OK && place.address_components) {
              const postalComponent = place.address_components.find(component =>
                component.types.includes("postal_code")
              );
              resolve(postalComponent ? postalComponent.long_name : null);
            } else {
              resolve(null);
            }
          }
        );
      });
    } catch (error) {
      console.error('Error getting postal code:', error);
      return null;
    }
  };

  // Update the handleSaveAddressConfirm function to include place_id and postal code
  const handleSaveAddressConfirm = async () => {
    if (!addressToSave || !saveAddressType) {
      setCommonAlert({ open: true, msg: t('please_select_address_type') });
      return;
    }

    if (saveAddressType === 'other' && (!addressName || addressName.trim() === '')) {
      setCommonAlert({ open: true, msg: t('please_enter_address_name') });
      return;
    }

    setSavingAddress(true);
    try {
      // Get postal code if place_id exists
      let postalCode = null;
      if (addressToSave.place_id) {
        postalCode = await getPostalCodeFromPlaceId(addressToSave.place_id);
      }

      const addressData = {
        lat: addressToSave.coords.lat,
        lng: addressToSave.coords.lng,
        description: addressToSave.description,
        name: saveAddressType === 'other' ? addressName.toLowerCase() : saveAddressType.toLowerCase(),
        place_id: addressToSave.place_id || null,
        postalCode: postalCode
      };

      // If admin, use selected user's ID, otherwise use current user's ID
      const targetUserId = (auth.profile.usertype === 'admin' && userCombo && userCombo.uid) 
        ? auth.profile.uid 
        : auth.profile.uid;

      await dispatch(editAddress(targetUserId, addressData, 'Add'));
      setSaveAddressModalOpen(false);
      setAddressToSave(null);
      setAddressName('');
      setSaveAddressType(''); 
      setCommonAlert({ open: true, msg: t('address_saved_successfully') });
    } catch (error) {
      setCommonAlert({ open: true, msg: t('error_saving_address') });
    } finally {
      setSavingAddress(false);
    }
  };

  const handleDeleteAddress = async (address) => {
    try {
      // If admin, use selected user's ID, otherwise use current user's ID
      const targetUserId = (auth.profile.usertype === 'admin' && userCombo && userCombo.uid) 
        ? userCombo.uid 
        : auth.profile.uid;

      await dispatch(editAddress(targetUserId, address, 'Delete'));
      setCommonAlert({ open: true, msg: t('address_deleted_successfully') });
    } catch (error) {
      setCommonAlert({ open: true, msg: t('error_deleting_address') });
    }
  };

  // Update the handleSelectSavedAddress function
  const handleSelectSavedAddress = async (address) => {
    let postalCode = address.postalCode || null;
    
    // If the saved address has a place_id but no postal code, fetch it
    if (address.place_id && !postalCode) {
      postalCode = await getPostalCodeFromPlaceId(address.place_id);
    }
    
    const addressData = {
      coords: { lat: address.lat, lng: address.lng },
      description: address.description,
      placeDetails: { 
        address_components: postalCode ? [
          {
            long_name: postalCode,
            types: ["postal_code"]
          }
        ] : []
      }
    };

    if (currentAddressField === 'pickup') {
      setPickupAddress(addressData);
    } else if (currentAddressField === 'drop') {
      setDropAddress(addressData);
    }
    setSavedAddressesModalOpen(false);
  };

  const handleOpenSavedAddresses = (fieldType) => {
    // For admin, ensure a user is selected and fetch that user's addresses only
    if (auth.profile.usertype === 'admin' || auth.profile.usertype === 'fleetadmin') {
      if (!userCombo || !userCombo.uid) {
        setCommonAlert({ open: true, msg: t('select_user') });
        return;
      }
      // Clear any stale addresses, then fetch selected user's addresses
      setSavedAddresses([]);
      dispatch(fetchAddresses(userCombo.uid));
    }
    setCurrentAddressField(fieldType);
    setSavedAddressesModalOpen(true);
  };

  const getAddressIcon = (name) => {
    switch (name.toLowerCase()) {
      case 'home':
        return <HomeIcon />;
      case 'work':
        return <WorkIcon />;
      default:
        return <LocationOnIcon />;
    }
  };

  const convertTo12Hour = (time24) => {
    let [hours, minutes] = time24.split(':');
    hours = parseInt(hours);
    
    const ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12;
    hours = hours ? hours : 12; // 0 should be 12
    
    return {
      time: `${hours.toString().padStart(2, '0')}:${minutes}`,
      ampm: ampm
    };
  };

  return (
    <div className={classes.container} ref={rootRef}>
      <Grid item xs={12} sm={12} md={8} lg={8}>
        <Grid item xs={12}>
            <Grid item xs={12} style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography className={classes.title} style={{textAlign:isRTL==='rtl'?'right':'left',fontFamily:FONT_FAMILY}}>
                  {t('add_booking_title')}
                </Typography>
                {auth.profile.usertype === 'admin' && (
                    <Button 
                    style={{marginRight:"10px", backgroundColor:"#06113c"}}
                      variant="contained" 
                      color="primary" 
                      onClick={() => navigate('/users/edituser/customer')}
                    >
                      {t('Add User')}
                    </Button>
                )}
              </Grid>
          </Grid>
        <div className={classes.container1}>
          <Grid container spacing={2} >
            <Grid item xs={12} >
              {users && auth.profile.usertype && auth.profile.usertype!=='customer' ?
                <UsersCombo
                  className={classes.items}
                  placeholder={t('User_Name_Mobile_number')}
                  users={users}
                  value={userCombo}
                  onChange={(event, newValue) => {
                    setUserCombo(newValue);
                  }}
                />
                : null}
            </Grid> 
            <Grid item xs={12} >
              <Box style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <GoogleMapsAutoComplete
                  variant={"outlined"}
                  placeholder={t('pickup_location')}
                  value={pickupAddress}
                  className={classes.items}
                  style={{width:"90%"}}
                  onChange={
                    (value) => {
                      setPickupAddress(value);
                    }
                  }
                />
                <IconButton
                  onClick={() => handleOpenSavedAddresses('pickup')}
                  style={{ backgroundColor: colors.LandingPage_Background, color: MAIN_COLOR }}
                  title={t('saved_addresses')}
                >
                  <BookmarkIcon />
                </IconButton>
                {pickupAddress && (
                  <IconButton
                    onClick={() => handleSaveAddress(pickupAddress, 'pickup')}
                    style={{ backgroundColor: MAIN_COLOR, color: colors.WHITE }}
                    title={t('save_address')}
                  >
                    <SaveIcon />
                  </IconButton>
                )}
              </Box>
              <span style={{fontSize:"small"}}>Please enter the full address</span>
            </Grid>
            <Grid item xs={12} >
              <Box style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <GoogleMapsAutoComplete placeholder={t('drop_location')}
                  variant={"outlined"}
                  value={dropAddress}
                  className={classes.items}
                  style={{width:"90%"}}
                  onChange={
                    (value) => {
                      setDropAddress(value);
                    }
                  }
                />
                <IconButton
                  onClick={() => handleOpenSavedAddresses('drop')}
                  style={{ backgroundColor: colors.LandingPage_Background, color: MAIN_COLOR }}
                  title={t('saved_addresses')}
                >
                  <BookmarkIcon />
                </IconButton>
                {dropAddress && (
                  <IconButton
                    onClick={() => handleSaveAddress(dropAddress, 'drop')}
                    style={{ backgroundColor: MAIN_COLOR, color: colors.WHITE }}
                    title={t('save_address')}
                  >
                    <SaveIcon />
                  </IconButton>
                )}
              </Box>
              <span style={{fontSize:"small"}}>Please enter the full address</span>
            </Grid>
            <Grid item xs={12} sm={6} >
              {cartypes ?
                  <Select
                     labelId="demo-simple-select-label"
                     id="demo-simple-select"
                      value={carType}
                      onChange={handleCarSelect}
                      variant="outlined"
                      fullWidth
                      style={{textAlign:isRTL==='rtl'? 'right':'left',}}
                      className={carType === t('select_car') ? classes.inputdimmed : classes.input}
                      sx={{color: colors.BLACK,
                          "roundTrip&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: MAIN_COLOR,
                      },}}
                     
                  >
                    <MenuItem dense={true} value={t('select_car')} key={t('select_car')} style={{direction:isRTL==='rtl'?'rtl':'ltr', width:'100%', justifyContent:'flex-start', paddingLeft:10,fontFamily:FONT_FAMILY}}>
                      {t('select_car')}
                    </MenuItem>
                    {
                      cartypes.sort((a, b) => a.pos - b.pos).map((car) =>
                        <MenuItem dense={true}  key={car.name} value={car.name} style={{direction:isRTL==='rtl'?'rtl':'ltr', width:'100%', justifyContent:'flex-start', paddingLeft:10,fontFamily:FONT_FAMILY}}>
                          <img src={car.image} className={isRTL==='rtl'? classes.carphotoRtl : classes.carphoto} alt="car types"/>{car.name}
                        </MenuItem>
                      )
                    }
                  </Select>
                : null}
            </Grid>
            <Grid item xs={12} sm={6}>
              <Select
                id="trip-type-select"
                value={tripType}
                onChange={handleTripTypeSelect}
                className={classes.input}
                style={{textAlign:isRTL==='rtl'? 'right':'left'}}
                sx={{color: colors.BLACK,
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderColor: MAIN_COLOR,
                  },
                }}
                variant="outlined"
                fullWidth
                inputProps={{ 'aria-label': 'Without label' }}
              >
                <MenuItem dense key={"ONE WAY TRIP"} value={"ONE WAY TRIP"} style={{direction:isRTL==='rtl'?'rtl':'ltr', width:'100%', justifyContent:'flex-start', paddingLeft:10,fontFamily:FONT_FAMILY}}>
                  {t('ONE WAY TRIP')}
                </MenuItem>
                <MenuItem dense key={"ROUND TRIP"} value={"ROUND TRIP"} style={{direction:isRTL==='rtl'?'rtl':'ltr', width:'100%', justifyContent:'flex-start', paddingLeft:10,fontFamily:FONT_FAMILY}}>
                  {t('ROUND TRIP')}
                </MenuItem>
              </Select>
            </Grid>
            {/* Outbound Journey Fields */}
            <Grid item xs={12} sm={6}>
              <TextField
                InputLabelProps={{ style: { fontFamily: FONT_FAMILY } }}
                id="date-local"
                label={tripType === 'ROUND TRIP' ? t('booking_date') : t('booking_date')}
                type="datetime-local"
                variant="outlined" 
                fullWidth
                className={[isRTL==='rtl'?classes.inputRtl:classes.commonInputStyle, classes.textField].join(" ")}
                InputProps={{
                  className: classes.input,
                  style:{textAlignLast:isRTL==='rtl'?'end':'start'}
                }}
                value={selectedDate}
                onChange={onDateChange}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                id="flight-number"
                label={tripType === 'ROUND TRIP' ? t('Flight No') : t('Flight No')}
                variant="outlined"
                fullWidth
                className={classes.textField}
                InputProps={{
                  className: classes.input,
                  style:{textAlignLast:isRTL==='rtl'?'end':'start'}
                }}
                onChange={(e) => setFlightNumber(e.target.value)}
              />
            </Grid>
            {/* Return Journey Fields - Only show for Round Trip */}
            {tripType === 'ROUND TRIP' && (
              <>
                <Grid item xs={12} sm={6}>
                  <TextField
                    InputLabelProps={{ style: { fontFamily: FONT_FAMILY } }}
                    id="return-date-picker"
                    label={t('Return Date')}
                    type="datetime-local"
                    variant="outlined"
                    fullWidth
                    className={[isRTL==='rtl'?classes.inputRtl:classes.commonInputStyle, classes.textField].join(" ")}
                    InputProps={{
                      className: classes.input,
                      style:{textAlignLast:isRTL==='rtl'?'end':'start'}
                    }}
                    value={returnDate}
                    onChange={onReturnDateChange}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    id="return-flight-number"
                    label={t('Return Flight No')}
                    variant="outlined"
                    fullWidth
                    className={classes.textField}
                    InputProps={{
                      className: classes.input,
                      style:{textAlignLast:isRTL==='rtl'?'end':'start'}
                    }}
                    onChange={(e) => setReturnFlightNumber(e.target.value)}
                  />
                </Grid>
              </>
            )}
            {/* <Grid item xs={12} sm={6}>
              <TextField
                id="Tips"
                label={t('Tips (optional)')}
                variant="outlined"
                fullWidth
                className={classes.textField}
                InputProps={{
                  className: classes.input,
                  style:{textAlignLast:isRTL==='rtl'?'end':'start'}
                }}
                onChange={(e) => setTips(e.target.value)}
              />
            </Grid> */}
            <Grid item xs={12} sm={12}>
              <TextField
                id="note"
                label={t('Note')}
                variant="outlined"
                fullWidth
                className={classes.textField}
                InputProps={{
                  className: classes.input,
                  style:{textAlignLast:isRTL==='rtl'?'end':'start'}
                }}
                onChange={(e) => setNoteText(e.target.value)}
              />
            </Grid>
            {bookingType === 'Book Later' ?
              <Grid item xs={12} sm={6} >
                <Button
                  size="lg"
                  onClick={handleGetOptions}
                  disabled={isBooked}
                  variant="contained" 
                  color="secondaryButton"
                  className={classes.buttonStyle}
                >
                  <i className="fas fa-car" style={isRTL ==='rtl' ? {marginLeft:5}:{marginRight:5}}/>
                  {t('book')}
                </Button>
              </Grid>
            :
              <Grid item xs={12} >
                <Button
                  size="lg"
                  onClick={handleGetOptions}
                  disabled={isBooked}
                  variant="contained" 
                  color="secondaryButton"
                  className={classes.buttonStyle}
                >
                  <i className="fas fa-car" style={isRTL ==='rtl' ? {marginLeft:5}:{marginRight:5}}/>
                  {t('book')} 
                </Button>
              </Grid>
            }
          </Grid>
        </div>
      </Grid>
      <Modal
        disablePortal
        disableEnforceFocus
        disableAutoFocus
        open={optionModalStatus}
        onClose={handleOptionModalClose}
        className={classes.modal}
        container={() => rootRef.current}
      >
        <Grid container spacing={2} className={classes.paper}>
          <Grid item xs={12} sm={12} md={12} lg={12} style={{textAlign:isRTL==='rtl'?'right':'left'}}>
          {selectedCarDetails && selectedCarDetails.parcelTypes?
            <FormControl component="fieldset">
              <FormLabel component="legend" style={{fontFamily:FONT_FAMILY,}}>{t('parcel_types')}</FormLabel>
              <RadioGroup name="parcelTypeIndex" value={instructionData.parcelTypeIndex} onChange={handleChange}>
                {selectedCarDetails.parcelTypes.map((element,index) =>
                  <FormControlLabel key={element.description} value={index} control={<Radio />} label={ <Typography className={classes.typography}>{ settings.swipe_symbol===false? settings.symbol + ' ' + element.amount + ' - ' + element.description: element.amount + ' ' + settings.symbol + ' - ' + element.description}</Typography>}/>
                )}
              </RadioGroup>
            </FormControl>
          :null}
          </Grid>
          <Grid item xs={12} sm={12} md={12} lg={12} style={{textAlign:isRTL==='rtl'?'right':'left'}}>
          {selectedCarDetails && selectedCarDetails.options?
            <FormControl component="fieldset">
              <FormLabel component="legend" style={{fontFamily:FONT_FAMILY,}}>{t('options')}</FormLabel>
              <RadioGroup name="optionIndex" value={instructionData.optionIndex} onChange={handleChange}>
                {selectedCarDetails.options.map((element,index) =>
                  <FormControlLabel key={element.description} value={index} control={<Radio />} label={ <Typography className={classes.typography}>{ settings.swipe_symbol===false? settings.symbol + ' ' + element.amount + ' - ' + element.description: element.amount + ' ' + settings.symbol + ' - ' + element.description}</Typography>} />
                )}
              </RadioGroup>
            </FormControl>
          :null}
          </Grid>
          <Grid item xs={12} sm={12} md={12} lg={12}  style={{textAlign:isRTL==='rtl'?'right':'left'}}>
          <Button onClick={handleOptionModalClose} variant="contained" color="primary" className={classes.buttonStyle2}>
            {t('cancel')}
          </Button>
          <Button onClick={handleGetEstimate} variant="contained" color="primary" style={isRTL==='rtl'?{marginRight:10}:{marginLeft:10}} className={classes.buttonStyle1}>
            {t('get_estimate')}
          </Button>
          </Grid>
        </Grid>
      </Modal>
      <Modal
        disablePortal
        disableEnforceFocus
        disableAutoFocus
        open={estimateModalStatus}
        onClose={handleEstimateModalClose}
        className={classes.modal}
        container={() => rootRef.current}
      >
        <Grid container spacing={1} className={classes.paper}>
          <BookingModalBody 
              classes={classes}  
              instructionData={instructionData} 
              handleChange={handleChange}
              tripInstructions={tripInstructions}
              roundTrip={roundTrip}
              auth={auth}
              profileData={profileData}
              settings={settings}
              deliveryWithBid={deliveryWithBid}
              otherPerson={otherPerson}
              setOtherPerson={setOtherPerson}
          />
          {showEst?
          <Grid item xs={12} sm={12} md={12} lg={12} style={{textAlign:isRTL==='rtl'?'right':'left'}}>
            {settings.swipe_symbol===false?
              <Typography color={'primary'} style={{fontSize:30, fontFamily:FONT_FAMILY,}}>
                {t('total')} - {settings?settings.symbol:null} {estimatedata.estimate ? 
                  (roundTrip === 1 ? ((parseFloat(estimatedata.estimate.estimateFare) * 2 + parseFloat(tips || 0)).toFixed(2)) : (parseFloat(estimatedata.estimate.estimateFare) + parseFloat(tips || 0)).toFixed(2)) 
                : null}
              </Typography>
              :
              <Typography color={'primary'} style={{fontSize:30, fontFamily:FONT_FAMILY,}}>
                {t('total')} - {estimatedata.estimate ? 
                  (roundTrip === 1 ? ((parseFloat(estimatedata.estimate.estimateFare) * 2 + parseFloat(tips || 0)).toFixed(2)) : (parseFloat(estimatedata.estimate.estimateFare) + parseFloat(tips || 0)).toFixed(2)) 
                : null} {settings?settings.symbol:null}
              </Typography>
            }
          </Grid>
          :null}
          {showEst && estimatedata.estimate && (estimatedata.estimate.special_fare > 0) ? (
    <Grid item xs={12} sm={12} md={12} lg={12} style={{ textAlign: isRTL === 'rtl' ? 'right' : 'left' }}>
      {settings.swipe_symbol === false ? 
        <Grid container spacing={2}>
          {estimatedata.estimate.special_fare > 0 && (
            <Grid item xs={6} style={{ textAlign: isRTL === 'rtl' ? 'right' : 'left' }}>
              <Typography color={'primary'} style={{ fontSize: 15, fontFamily: FONT_FAMILY, color: colors.BLACK, fontWeight: 'bold' }}>
                {"Sub Total"} - {settings ? settings.symbol : null} {
                  roundTrip === 1 ? 
                  (estimatedata.estimate.subTotal * 2) : 
                  estimatedata.estimate.subTotal
                }
              </Typography>
            </Grid>
          )}
          {estimatedata.estimate.special_fare > 0 && (
            <Grid item xs={6} style={{ textAlign: isRTL === 'rtl' ? 'left' : 'right' }}>
              <Typography color={'primary'} style={{ fontSize: 15, fontFamily: FONT_FAMILY, color: colors.BLACK, fontWeight: 'bold' }}>
                {"Van Fare"} - {settings ? settings.symbol : null} {
                  roundTrip === 1 ? 
                  (estimatedata.estimate.special_fare * 2) : 
                  estimatedata.estimate.special_fare
                }
              </Typography>
            </Grid>
          )}
        </Grid>
        :
        <Grid container spacing={2}>
          {estimatedata.estimate.special_fare > 0 && (
            <Grid item xs={6} style={{ textAlign: isRTL === 'rtl' ? 'right' : 'left' }}>
              <Typography color={'primary'} style={{ fontSize: 15, fontFamily: FONT_FAMILY, color: colors.BLACK, fontWeight: 'bold' }}>
                {"Sub Total"} - {
                  roundTrip === 1 ? 
                  (estimatedata.estimate.subTotal * 2) : 
                  estimatedata.estimate.subTotal
                } {settings ? settings.symbol : null}
              </Typography>
            </Grid>
          )}
          {estimatedata.estimate.special_fare > 0 && (
            <Grid item xs={6} style={{ textAlign: isRTL === 'rtl' ? 'left' : 'right' }}>
              <Typography color={'primary'} style={{ fontSize: 15, fontFamily: FONT_FAMILY, color: colors.BLACK, fontWeight: 'bold' }}>
                {"Van Fare"} - {
                  roundTrip === 1 ? 
                  (estimatedata.estimate.special_fare * 2) : 
                  estimatedata.estimate.special_fare
                } {settings ? settings.symbol : null}
              </Typography>
            </Grid>
          )}
        </Grid>
      }
    </Grid>
  ) : null}
<Grid item xs={12} sm={12} md={12} lg={12} style={{ textAlign: isRTL === 'rtl' ? 'right' : 'left' }}>
      
      <Grid container spacing={2}>
        
        {parseFloat(tips || 0) > 0 && (
          <Grid item xs={12} style={{ textAlign: isRTL === 'rtl' ? 'right' : 'left' }}>
            <Typography color={'primary'} style={{ fontSize: 15, fontFamily: FONT_FAMILY, color: colors.BLACK, fontWeight: 'bold' }}>
              {"Tips"} - {settings ? settings.symbol : null} {parseFloat(tips).toFixed(2)}
            </Typography>
          </Grid>
        )}
      </Grid>
      </Grid>
          {auth.profile.usertype === 'customer'?
          <Grid item xs={12} sm={12} md={12} lg={12} style={{textAlign:isRTL==='rtl'?'right':'left'}}>
            <FormControl component="fieldset">
              {/* <FormLabel component="legend" style={{fontFamily:FONT_FAMILY,}}>{t('payment_mode')}</FormLabel>
              <RadioGroup name="payment_mode" value={payment_mode} onChange={handleChange}>
                {radioProps.map((element,index) =>
                  <FormControlLabel key={element.cat} value={index} control={<Radio />} 
                  label={ <Typography className={classes.typography}>{element.label}</Typography>} 
                  />
                )}
              </RadioGroup> */}
            </FormControl>
          </Grid>
          :null}
          <Grid item xs={12} sm={12} md={12} lg={12} style={{textAlign:isRTL==='rtl'?'right':'left'}}>
          {loading?
            <DialogActions style={{justifyContent:'center', alignContent:'center'}}>
              <CircularProgress/>
            </DialogActions>
          :
            <Grid item xs={12} >
              <Button onClick={handleEstimateModalClose} variant="contained" style={{ backgroundColor:colors.RED, fontFamily:FONT_FAMILY }}>
                {t('cancel')}
              </Button>
              <Button onClick={confirmBooking} variant="contained"  style={isRTL==='rtl'?{marginRight:10, backgroundColor:colors.GREEN}:{marginLeft:10, backgroundColor:colors.GREEN, fontFamily:FONT_FAMILY}}>
                {t('book_now')}
              </Button>
            </Grid>
          }
          </Grid>
        </Grid>
      </Modal>

      <Modal
        disablePortal
        disableEnforceFocus
        disableAutoFocus
        open={walletModalStatus}
        onClose={handleWalletModalClose}
        className={classes.modal}
        container={() => rootRef.current}
      >
        <Grid container spacing={2} className={classes.paper}>
          <Grid item xs={12} sm={12} md={12} lg={12} style={{marginBottom: '20px'}}>
            <Typography  style={{fontWeight:'bolder', marginBottom:10}}>
              {t('payment')}
            </Typography>
            {bookingdata && bookingdata.booking?
            <Typography color={"primary"} style={{fontSize:30}}>
              { (settings.swipe_symbol===false? settings.symbol + bookingdata.booking.mainData.trip_cost :  bookingdata.booking.mainData.trip_cost + settings.symbol) }
            </Typography>
            :null}
            <Typography >
              {t('use_wallet_balance') + " " + (settings.swipe_symbol===false? settings.symbol + auth.profile.walletBalance :  auth.profile.walletBalance + settings.symbol) + ")"}
            </Typography>
          </Grid>            
          <Grid item xs={12} sm={12} md={12} lg={12}>
            <Button onClick={handleWalletModalClose} variant="contained" style={{backgroundColor:colors.RED }}>
              {t('cancel')}
            </Button>
            <Button variant="contained" color="primary" style={{marginLeft:10,backgroundColor:colors.GREEN}} onClick={handleWalletPayment}>
              {t('paynow_button')}
            </Button>
          </Grid>
        </Grid>
      </Modal>
      <Modal
        disablePortal
        disableEnforceFocus
        disableAutoFocus
        open={paymentModalStatus}
        onClose={handlePaymentModalClose}
        className={classes.modal}
        container={() => rootRef.current}
      >
        <Grid container spacing={2} className={classes.paper}>
          {providers && selectedProvider && bookingdata && bookingdata.booking?
          <form action={selectedProvider.link} method="POST">
            <input type='hidden' name='order_id' value={bookingdata.booking.booking_id}/>
            <input type='hidden' name='amount' value={bookingdata.booking.mainData.trip_cost}/>
            <input type='hidden' name='currency' value={settings.code}/>
            <input type='hidden' name='product_name' value={t('bookingPayment')}/>
            <input type='hidden' name='first_name' value={auth.profile.firstName}/>
            <input type='hidden' name='last_name' value={auth.profile.lastName}/>
            <input type='hidden' name='quantity' value={1}/>
            <input type='hidden' name='cust_id' value={bookingdata.booking.mainData.customer}/>
            <input type='hidden' name='mobile_no' value={bookingdata.booking.mainData.customer_contact}/>
            <input type='hidden' name='email' value={bookingdata.booking.mainData.customer_email}/>
            <Grid item xs={12} sm={12} md={12} lg={12} style={{marginBottom: '20px'}}>
              <FormControl fullWidth>
              <FormLabel component="legend">{t('payment')}</FormLabel>
              <Select
                  id="selectedProviderIndex"
                  name= "selectedProviderIndex"
                  value={selectedProviderIndex}
                  label={t('payment')}
                  onChange={handleChange}
                  style={{textAlign:isRTL==='rtl'? 'right':'left'}}
                  inputProps={{ 'aria-label': 'Without label' }}
                >
                  {(Array.isArray(providers) ? providers : []).map((provider, index) =>
                    <MenuItem key={provider.name} value={index}>
                      <img style={{height:24, margin:7}} src={icons[provider.name]} alt={provider.name}/> 
                    </MenuItem>
                  )}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={12} md={12} lg={12}>
            <Button onClick={handlePaymentModalClose} variant="contained" color="primary">
              {t('cancel')}
            </Button>
            <Button variant="contained" color="primary" type="submit" style={{marginLeft:10}} onClick={handlePaymentModalClose}>
              {t('paynow_button')}
            </Button>
            </Grid>
          </form>
          :null}
        </Grid>
      </Modal>
      <AlertDialog open={bookingdata.error.flag} onClose={handleBookingErrorClose}>{bookingdata.error.msg}</AlertDialog>
      <AlertDialog open={estimatedata.error.flag} onClose={handleEstimateErrorClose}>{estimatedata.error.msg}</AlertDialog>
      <AlertDialog open={commonAlert.open} onClose={handleCommonAlertClose}>{commonAlert.msg}</AlertDialog>

      {/* Save Address Modal */}
      <Modal
        open={saveAddressModalOpen}
        onClose={() => setSaveAddressModalOpen(false)}
        className={classes.modal}
        container={() => rootRef.current}
      >
        <Grid container spacing={2} className={classes.paper}>
          <Grid item xs={12}>
            <Typography variant="h6" style={{ fontFamily: FONT_FAMILY, marginBottom: 20 }}>
              {t('save_address')}
            </Typography>
            <Typography variant="body2" style={{ fontFamily: FONT_FAMILY, marginBottom: 20 }}>
              {addressToSave?.description}
            </Typography>
            
            <FormControl component="fieldset" fullWidth style={{ marginBottom: 20 }}>
              <FormLabel component="legend" style={{ fontFamily: FONT_FAMILY }}>
                {t('address_type')}
              </FormLabel>
              <RadioGroup
                value={saveAddressType}
                onChange={(e) => setSaveAddressType(e.target.value)}
                style={{ flexDirection: 'row', gap: 20 }}
              >
                <FormControlLabel
                  value="home"
                  control={<Radio />}
                  label={
                    <Box style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                      <HomeIcon />
                      <Typography style={{ fontFamily: FONT_FAMILY }}>{t('home')}</Typography>
                    </Box>
                  }
                />
                <FormControlLabel
                  value="work"
                  control={<Radio />}
                  label={
                    <Box style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                      <WorkIcon />
                      <Typography style={{ fontFamily: FONT_FAMILY }}>{t('work')}</Typography>
                    </Box>
                  }
                />
                <FormControlLabel
                  value="other"
                  control={<Radio />}
                  label={
                    <Box style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                      <LocationOnIcon />
                      <Typography style={{ fontFamily: FONT_FAMILY }}>{t('other')}</Typography>
                    </Box>
                  }
                />
              </RadioGroup>
            </FormControl>

            {saveAddressType === 'other' && (
              <TextField
                fullWidth
                label={t('address_name')}
                value={addressName}
                onChange={(e) => setAddressName(e.target.value)}
                variant="outlined"
                className={classes.textField}
                style={{ marginBottom: 20 }}
              />
            )}

            <Box style={{ display: 'flex', gap: 10, justifyContent: 'flex-end' }}>
              <Button
                onClick={() => setSaveAddressModalOpen(false)}
                variant="contained"
                style={{ backgroundColor: colors.RED, fontFamily: FONT_FAMILY }}
              >
                {t('cancel')}
              </Button>
              <Button
                onClick={handleSaveAddressConfirm}
                variant="contained"
                disabled={savingAddress}
                style={{ backgroundColor: MAIN_COLOR, fontFamily: FONT_FAMILY }}
              >
                {savingAddress ? <CircularProgress size={20} color="inherit" /> : t('save')}
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Modal>

      {/* Saved Addresses Modal */}
      <Modal
        open={savedAddressesModalOpen}
        onClose={() => setSavedAddressesModalOpen(false)}
        className={classes.modal}
        container={() => rootRef.current}
      >
        <Grid container spacing={2} className={classes.paper}>
          <Grid item xs={12}>
            <Typography variant="h6" style={{ fontFamily: FONT_FAMILY, marginBottom: 20 }}>
              {t('saved_addresses')}
            </Typography>
            
            {savedAddresses && savedAddresses.length > 0 ? (
              <List>
                {savedAddresses.map((address, index) => (
                  <ListItem
                    key={index}
                    style={{
                      border: '1px solid #e0e0e0',
                      borderRadius: 8,
                      marginBottom: 8,
                      cursor: 'pointer'
                    }}
                    onClick={() => handleSelectSavedAddress(address)}
                  >
                    <ListItemIcon>
                      {getAddressIcon(address.name)}
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Typography style={{ fontFamily: FONT_FAMILY, fontWeight: 'bold' }}>
                          {address.name.toUpperCase()}
                        </Typography>
                      }
                      secondary={
                        <Typography style={{ fontFamily: FONT_FAMILY }}>
                          {address.description}
                        </Typography>
                      }
                    />
                    <IconButton
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteAddress(address);
                      }}
                      size="small"
                      style={{ color: colors.RED }}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </ListItem>
                ))}
              </List>
            ) : (
              <Typography style={{ fontFamily: FONT_FAMILY, textAlign: 'center', color: colors.CARD_LABEL }}>
                {t('no_saved_addresses')}
              </Typography>
            )}

            <Box style={{ display: 'flex', justifyContent: 'flex-end', marginTop: 20 }}>
              <Button
                onClick={() => setSavedAddressesModalOpen(false)}
                variant="contained"
                style={{ backgroundColor: MAIN_COLOR, fontFamily: FONT_FAMILY }}
              >
                {t('close')}
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Modal>

      <ToastContainer position="bottom-end" className="p-3">
        <Toast 
          style={{ 
            backgroundColor: '#d4edda', // Light green background for success
            color: '#155724', // Dark green text
            border: '1px solid #c3e6cb', // Light green border
            borderRadius: '5px', // Rounded corners
            boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)' // Subtle shadow
          }} 
          onClose={() => setShowToast(false)} 
          show={showToast} 
          delay={2000} 
          autohide
        >
          <Toast.Body style={{ fontWeight: 'bold' }}>{toastMessage}</Toast.Body>
        </Toast>
      </ToastContainer>
    </div>
  );
}
